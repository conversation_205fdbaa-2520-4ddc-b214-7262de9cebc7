# Generated by Django 5.2 on 2025-06-02 23:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("invoice_generator", "0002_salesworkflow_is_temporary_template"),
    ]

    operations = [
        migrations.AddField(
            model_name="salesworkflow",
            name="temporary_template_data",
            field=models.JSONField(
                blank=True,
                default=dict,
                help_text="Temporary template data for workflows that don't save templates",
            ),
        ),
        migrations.AlterField(
            model_name="salesworkflow",
            name="company_template",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="workflows",
                to="invoice_generator.companytemplate",
            ),
        ),
    ]
