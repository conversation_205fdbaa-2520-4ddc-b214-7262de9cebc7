# Generated by Django 5.2 on 2025-06-03 00:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("invoice_generator", "0003_salesworkflow_temporary_template_data_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="salesinvoicedata",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="salesinvoicedata",
            name="user",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="sales_invoice_data",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=False,
        ),
        migrations.RemoveField(
            model_name="salesinvoicedata",
            name="workflow",
        ),
        migrations.AlterUniqueTogether(
            name="salesinvoicedata",
            unique_together={("user", "row_number", "created_at")},
        ),
        migrations.DeleteModel(
            name="SalesWorkflow",
        ),
    ]
