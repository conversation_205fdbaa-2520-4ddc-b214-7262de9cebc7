from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import EmailValidator

User = get_user_model()


class CompanyTemplate(models.Model):
    """Model to store company details and template associations for invoice generation."""

    # User association
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="company_templates"
    )

    # Template information
    template_name = models.Char<PERSON>ield(
        max_length=100, help_text="User-defined name for this template"
    )
    template_id = models.CharField(
        max_length=50, help_text="ID of the selected MizuFlow template"
    )
    template_display_name = models.Char<PERSON>ield(
        max_length=100, help_text="Display name of the MizuFlow template"
    )

    # Company information
    company_name = models.Char<PERSON>ield(max_length=200)
    address_line_1 = models.CharField(max_length=200)
    address_line_2 = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100)
    state_province = models.Char<PERSON><PERSON>(max_length=100)
    postal_code = models.Char<PERSON>ield(max_length=20)
    country = models.Char<PERSON><PERSON>(max_length=100)
    phone = models.Char<PERSON>ield(max_length=20, blank=True)
    email = models.EmailField(validators=[EmailValidator()])
    website = models.URLField(blank=True)

    # Payment and banking information
    default_payment_terms = models.CharField(max_length=100, default="Net 30 days")
    bank_name = models.CharField(max_length=100, blank=True)
    account_number = models.CharField(max_length=50, blank=True)
    routing_number = models.CharField(max_length=20, blank=True)
    swift_code = models.CharField(max_length=20, blank=True)

    # Business information
    tax_id = models.CharField(max_length=50, blank=True)
    business_registration = models.CharField(max_length=100, blank=True)

    # Logo (stored as file path or URL)
    logo_url = models.URLField(blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_used = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["-last_used", "-updated_at"]
        unique_together = ["user", "template_name"]

    def __str__(self):
        return f"{self.template_name} - {self.company_name}"


class SalesInvoiceData(models.Model):
    """Model to store individual sales invoice data from CSV processing."""

    # User association
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="sales_invoice_data"
    )

    # Row information
    row_number = models.IntegerField(help_text="Row number in the original CSV file")

    # Invoice data (stored as JSON for flexibility)
    invoice_data = models.JSONField(help_text="Processed invoice data mapped from CSV")

    # Processing status
    is_processed = models.BooleanField(default=False)
    processing_error = models.TextField(blank=True)

    # Generated invoice information
    generated_invoice_path = models.CharField(max_length=500, blank=True)
    invoice_number = models.CharField(max_length=50, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["row_number"]
        unique_together = ["user", "row_number", "created_at"]

    def __str__(self):
        return (
            f"Invoice Data {self.pk} - Row {self.row_number} (User {self.user.email})"
        )
