import Mustache from "mustache";
import parse from "html-react-parser";
import { motion } from "framer-motion";
import { toast } from "react-hot-toast";
import { useEffect, useState } from "react";
import { TbArrowLeft, TbRefresh } from "react-icons/tb";

const MizuTemplatePreview = ({
  selectedTemplate,
  companyData,
  onBack,
  onReset,
  onProceed,
  onStartWorkflow,
  variants
}) => {
  const [renderedHtml, setRenderedHtml] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [startingWorkflow, setStartingWorkflow] = useState(false);

  // Preview data combining user's company data with static sample data
  const staticInvoiceData = {
    // Company fields - use actual user data from CompanyDetailsForm
    company_name: companyData?.company_name || "Your Company Name",
    company_address_line_1: companyData?.address_line_1 || "123 Business Street",
    company_city: companyData?.city || "Business City",
    company_state_province: companyData?.state_province || "BC",
    company_postal_code: companyData?.postal_code || "12345",
    company_country: companyData?.country || "Canada",
    company_phone: companyData?.phone || "+****************",
    company_email: companyData?.email || "<EMAIL>",
    company_website: companyData?.website || "www.yourcompany.com",

    // Logo - use actual user logo if available
    company_logo_url: companyData?.logo_url || null,
    has_logo: !!companyData?.logo_url,

    // Client fields - static sample data
    client_company: "Acme Corporation",
    client_address: "456 Client Street, Business City, BC 12345",
    contact_email: "<EMAIL>",
    phone_number: "+****************",

    // Invoice fields - static sample data
    invoice_number: "INV-2024-001",
    bill_date: "2024-01-15",
    due_date: "2024-02-14",
    order_reference: "PO-12345",
    payment_terms: companyData?.default_payment_terms || "Net 30",

    // Line items array - static sample data
    line_items: [
      {
        service_description: "Web Development Services",
        quantity: 40,
        unit_rate: "50.00",
        line_amount: "2000.00"
      },
      {
        service_description: "UI/UX Design Consultation",
        quantity: 10,
        unit_rate: "75.00",
        line_amount: "750.00"
      },
      {
        service_description: "Database Setup & Configuration",
        quantity: 8,
        unit_rate: "65.00",
        line_amount: "520.00"
      },
      {
        service_description: "API Integration Services",
        quantity: 12,
        unit_rate: "80.00",
        line_amount: "960.00"
      },
      {
        service_description: "Testing & Quality Assurance",
        quantity: 15,
        unit_rate: "45.00",
        line_amount: "675.00"
      }
    ],

    // Totals - static sample data
    subtotal: "4905.00",
    total_tax: "416.93",
    total_discount: null, // null will hide the discount section
    total_amount: "5321.93",

    // Footer fields - use actual user data where available
    bank_name: companyData?.bank_name || "Business Bank",
    account_number: companyData?.account_number || "*********",
    routing_number: companyData?.routing_number || "*********",
    tax_id: companyData?.tax_id || "TAX*********",
    business_registration: companyData?.business_registration || "REG*********"
  };

  useEffect(() => {
    if (selectedTemplate && companyData) {
      renderTemplate();
    }
  }, [selectedTemplate, companyData]);

  // Mustache template renderer
  const renderTemplateWithMustache = (template, data) => {
    try {
      console.log('Starting mustache template rendering with data:', data);
      console.log('Template length:', template.length);

      // Use Mustache to render the template
      const rendered = Mustache.render(template, data);

      console.log('Mustache rendering completed');
      console.log('Rendered HTML length:', rendered.length);

      return rendered;
    } catch (error) {
      console.error('Mustache rendering error:', error);
      throw new Error(`Template rendering failed: ${error.message}`);
    }
  };

  // Function to extract body content and styles from full HTML document
  // This prevents React from trying to mount multiple <html> and <body> elements
  // which causes the "mounting a new html/body component" error
  const extractBodyContentWithStyles = (htmlContent) => {
    try {
      // Extract styles from head section
      const styleMatch = htmlContent.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
      const styles = styleMatch ? styleMatch[1] : '';

      // Extract content from body section
      const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
      const bodyContent = bodyMatch ? bodyMatch[1] : htmlContent;

      // Combine styles and body content
      const processedHtml = styles
        ? `<style>${styles}</style>${bodyContent}`
        : bodyContent;

      return processedHtml;
    } catch (error) {
      console.error('Error extracting body content:', error);
      // Fallback: return original content if extraction fails
      return htmlContent;
    }
  };

  // Template renderer function using mustache
  const renderTemplate = async () => {
    try {
      setLoading(true);
      setError("");

      // Use mustache template rendering
      const rendered = renderTemplateWithMustache(
        selectedTemplate.html_content,
        staticInvoiceData
      );

      // Extract only body content and styles to avoid html/body tag conflicts
      const processedHtml = extractBodyContentWithStyles(rendered);

      console.log("Template rendering completed");
      console.log("Static data:", staticInvoiceData);
      console.log("Rendered HTML length:", processedHtml.length);

      setRenderedHtml(processedHtml);
    } catch (err) {
      console.error("Template rendering error:", err);
      setError("Failed to render template: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleProceed = () => {
    // Pass the rendered data to the next step
    onProceed({
      template: selectedTemplate,
      companyData: companyData,
      staticData: staticInvoiceData,
      renderedHtml: renderedHtml
    });
  };

  const handleStartWorkflow = async () => {
    if (!companyData?.savedTemplate) {
      toast.error("Company template not found. Please go back and save your company details.");
      return;
    }

    setStartingWorkflow(true);

    try {
      const savedTemplate = companyData.savedTemplate;
      const isTemporary = savedTemplate.id && savedTemplate.id.toString().startsWith('temp_');

      if (isTemporary) {
        // For temporary templates, pass the template data directly
        const temporaryTemplateData = {
          template_id: selectedTemplate.id,
          template_display_name: selectedTemplate.name,
          company_name: savedTemplate.company_name,
          address_line_1: savedTemplate.address_line_1,
          address_line_2: savedTemplate.address_line_2 || '',
          city: savedTemplate.city,
          state_province: savedTemplate.state_province,
          postal_code: savedTemplate.postal_code,
          country: savedTemplate.country,
          phone: savedTemplate.phone || '',
          email: savedTemplate.email,
          website: savedTemplate.website || '',
          default_payment_terms: savedTemplate.default_payment_terms,
          bank_name: savedTemplate.bank_name || '',
          account_number: savedTemplate.account_number || '',
          routing_number: savedTemplate.routing_number || '',
          swift_code: savedTemplate.swift_code || '',
          tax_id: savedTemplate.tax_id || '',
          business_registration: savedTemplate.business_registration || '',
          logo_url: savedTemplate.logo_url || null
        };

        toast.success("Ready to start sales workflow!");

        // Pass the temporary template data directly to the parent component
        onStartWorkflow({
          temporaryTemplateData: temporaryTemplateData,
          companyTemplate: null,
          selectedTemplate: selectedTemplate
        });
      } else {
        // For saved templates, use the company template
        toast.success("Ready to start sales workflow!");

        // Pass the saved company template to the parent component
        onStartWorkflow({
          temporaryTemplateData: null,
          companyTemplate: savedTemplate,
          selectedTemplate: selectedTemplate
        });
      }
    } catch (error) {
      console.error("Error starting workflow:", error);
      const errorMessage = error.response?.data?.error || "Failed to start sales workflow. Please try again.";
      toast.error(errorMessage);
    } finally {
      setStartingWorkflow(false);
    }
  };

  if (loading) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center py-12"
        variants={variants}
      >
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p className="mt-4 text-gray-600">Rendering template...</p>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center py-12"
        variants={variants}
      >
        <div className="text-red-600 text-center">
          <p className="text-lg font-medium mb-2">Template Rendering Error</p>
          <p className="text-sm mb-4">{error}</p>
          <button
            onClick={renderTemplate}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mr-2"
          >
            Try Again
          </button>
          <button
            onClick={onBack}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="flex flex-col gap-3"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <motion.button
            onClick={onBack}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-600 text-white shadow-md hover:bg-blue-700 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Go back"
          >
            <TbArrowLeft className="h-6 w-6" />
          </motion.button>
          <div>
            <h2 className="text-xl font-medium text-gray-700">
              Template Preview
            </h2>
            <p className="text-gray-600 mt-1">
              Preview of {selectedTemplate.name} template with your company data
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <motion.button
            onClick={onReset}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-red-100 text-red-600 hover:bg-red-200 shadow-md transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Reset process"
          >
            <TbRefresh className="h-6 w-6" />
          </motion.button>

          <button
            onClick={handleStartWorkflow}
            disabled={startingWorkflow}
            className={`px-6 py-2 font-medium rounded-lg transition-colors ${
              startingWorkflow
                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {startingWorkflow ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Starting...
              </div>
            ) : (
              "Continue"
            )}
          </button>
        </div>
      </div>

      {/* Template Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div>
            <h3 className="font-medium text-blue-900">{selectedTemplate.name}</h3>
            <p className="text-sm text-blue-700">{selectedTemplate.description}</p>
          </div>
        </div>
      </div>

      {/* Preview Container */}
      <div className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="bg-white px-4 py-2 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Invoice Preview</span>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span>Sample data is used for demonstration</span>
            </div>
          </div>
        </div>

        <div className="p-1 overflow-auto" style={{ maxHeight: '800px', minHeight: '600px' }}>
          {renderedHtml && (
            <div className="invoice-preview" style={{
              fontSize: '14px',
              lineHeight: '1.5',
              transform: 'scale(0.95)',
              transformOrigin: 'top center',
              width: '100%',
              maxWidth: '100%',
              margin: '0',
              backgroundColor: 'white',
              borderRadius: '4px'
            }}>
              {parse(renderedHtml)}
            </div>
          )}
        </div>
      </div>



      {/* Note */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
        <div className="flex items-start gap-2">
          <svg className="w-4 h-4 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <p className="text-xs text-yellow-800">
              <strong>Note:</strong> This is a preview with sample data. In the actual invoice generation,
              you&apos;ll be able to customize all the invoice details, line items, and client information.
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default MizuTemplatePreview;
